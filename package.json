{"name": "cnc-web-fe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "restyle": "npx prettier --write 'src/**/*.{ts,tsx,json,md}'"}, "dependencies": {"@headlessui/react": "^2.2.7", "@heroicons/react": "^2.0.18", "@next/third-parties": "^15.3.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "@segment/snippet": "^4.16.2", "@types/node": "20.4.5", "@types/react": "18.2.17", "@types/react-dom": "18.2.7", "autoprefixer": "10.4.14", "class-variance-authority": "^0.7.0", "classnames": "^2.3.2", "dayjs": "^1.11.9", "eslint": "8.45.0", "eslint-config-next": "^15.5.2", "hls.js": "^1.5.20", "html-react-parser": "^5.2.6", "isomorphic-dompurify": "^1.8.0", "lodash": "^4.17.21", "next": "^15.5.2", "next-nprogress-bar": "^2.1.2", "postcss": "8.4.27", "react": "^19.1.1", "react-datepicker": "^4.16.0", "react-day-picker": "8.10.1", "react-dom": "^19.1.1", "react-hot-toast": "^2.5.2", "react-responsive": "^10.0.1", "react-slick": "^0.29.0", "sharp": "^0.32.5", "slick-carousel": "^1.8.1", "tailwindcss": "3.3.3", "typescript": "5.1.6", "vaul": "^1.1.2"}, "devDependencies": {"@trivago/prettier-plugin-sort-imports": "^4.2.0", "@types/lodash": "^4.14.196", "@types/react-datepicker": "^4.15.0", "@types/react-slick": "^0.23.10", "daisyui": "^3.5.0", "eslint-config-prettier": "^8.9.0", "prettier": "^3.0.1", "prettier-plugin-tailwindcss": "^0.4.1", "tailwind-merge": "^1.14.0"}}