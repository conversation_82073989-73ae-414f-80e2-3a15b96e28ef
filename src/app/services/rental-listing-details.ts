import { Nullable } from '@/types/common';

const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';

export const getListingDetailsByNeighborhoodAndSlug = async <T>(slug: string) => {
  const res = await fetch(`${BASE_URL}/listings-by-slug/${slug}?show_ratings`, {
    next: { revalidate: 10, tags: [slug] },
  });

  if (!res.ok) {
    console.debug('Failed to fetch data', slug);
    return null;
  }

  return res.json() as T;
};

type SubmitBookingRequestData = {
  phone?: string;
  email: string;
  first_name: string;
  last_name: string;
  comment: string;
  contact_method: string;
  neighborhood: string;
  listing_id: number;
  bedroom_number?: number;
  max_price?: number;
  guest?: number;
  children?: number;
  bedrooms?: number;
  property_address: string;
  interest?: string;
  arrival_date?: string;
  departure_date?: string;
  pet_type?: string;
  pet_description?: string;
  pet_count?: number;
  flexibility?: Nullable<string>;
  source: string;
};

type ContactAgentPayload = {
  phone: string;
  email: string;
  first_name: string;
  last_name: string;
  how_can_we_help: string;
  contact_method: string;
  neighborhood: string;
  listing_id: number;
  property_address: string;
};

export const submitBookingRequest = async (data: SubmitBookingRequestData) => {
  const res = await fetch(`${BASE_URL}/get-in-touch`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!res.ok) {
    throw new Error('Failed to Submit Booking Request');
  }

  return res.json();
};

export const contactAgent = async (data: ContactAgentPayload) => {
  const res = await fetch(`${BASE_URL}/contact-agent`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!res.ok) {
    throw new Error('Failed to Submit Contact Agent Request');
  }

  return res.json();
};

export const calculateRentForListing = async <T>(payload: {
  arrival_date: string;
  departure_date: string;
  listing: number;
}) => {
  try {
    const res = await fetch(`${BASE_URL}/calculate-rent`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    console.log('the res is', res);

    if (!res.ok) {
      const errorData = await res.json();
      throw new Error(errorData.message || 'Failed to calculate rent');
    }

    return (await res.json()) as T;
  } catch (error) {
    return Promise.reject(error);
  }
};

export type GuestLeasePayload = {
  rent: number;
  listing_id: number;
  arrival_date: string;
  departure_date: string;
  payment_method: 'ach' | 'credit_card';
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  address: string;
  adult_count: number;
  child_count: number;
  bringing_pets: boolean;
  pet_type?: Nullable<string>;
  pet_count?: Nullable<number>;
  pet_description?: Nullable<string>;
  travel_insurance: Nullable<boolean>;
  return_url: string;
  source: string;
};

export const createGuestLease = async (data: GuestLeasePayload) => {
  const res = await fetch(`${BASE_URL}/guest-leases`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!res.ok) {
    throw new Error('Failed to Submit Booking Request');
  }

  return res.json();
};

type BuyerInterestPayload = {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  timeframe?: Nullable<string>;
  budget_range?: Nullable<string>;
  send_similar_homes?: boolean;
  listing: Nullable<number>;
  source: string;
};

export const submitBuyerInterest = async (data: BuyerInterestPayload) => {
  const res = await fetch(`${BASE_URL}/buyer-interest`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!res.ok) {
    throw new Error('Failed to Submit Buyer Interest Request');
  }

  return res.json();
};
