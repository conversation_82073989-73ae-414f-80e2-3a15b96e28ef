import CuisineFilterCheckboxes from '@/clients/components/nantucket-dining-guide/CuisineFilterCheckboxes';

type PageProps = {
  params: Promise<{ slug: string | string[] }>;
};

export default async function NantucketDiningGuide({ params }: PageProps) {
  return (
    <main className='py-10 container'>
      <h1 className='my-1 text-3xl text-center font-semibold text-[#38425b]'>
        Where to eat, drink and shop for food on Nantucket
      </h1>
      <div className='flex items-center gap-x-4 mt-[50px]'>
        <div className='w-[262px] rounded border border-solid border-[#e1e9ef]'>
          <p className='text-xl px-5 pt-2.5 mb-2.5 font-medium'>Filter</p>
          <hr className='my-2' />
          <CuisineFilterCheckboxes />
        </div>
      </div>
    </main>
  );
}
