'use client';

import { useState } from 'react';
import Checkbox from '@/clients/ui/checkbox';

export default function TestCheckboxPage() {
  const [checked1, setChecked1] = useState(false);
  const [checked2, setChecked2] = useState(true);
  const [checked3, setChecked3] = useState(false);

  return (
    <main className='py-10 container'>
      <h1 className='text-3xl font-bold mb-8'>Checkbox Test Page</h1>
      
      <div className='space-y-6'>
        <div className='space-y-4'>
          <h2 className='text-xl font-semibold'>Basic Checkboxes</h2>
          
          <div className='flex items-center space-x-2'>
            <Checkbox 
              id='test1' 
              checked={checked1}
              onChange={(e) => setChecked1(e.target.checked)}
            />
            <label htmlFor='test1' className='cursor-pointer'>
              Controlled checkbox (unchecked by default)
            </label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox 
              id='test2' 
              checked={checked2}
              onChange={(e) => setChecked2(e.target.checked)}
            />
            <label htmlFor='test2' className='cursor-pointer'>
              Controlled checkbox (checked by default)
            </label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox id='test3' />
            <label htmlFor='test3' className='cursor-pointer'>
              Uncontrolled checkbox
            </label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox id='test4' defaultChecked />
            <label htmlFor='test4' className='cursor-pointer'>
              Uncontrolled checkbox (default checked)
            </label>
          </div>
        </div>

        <div className='space-y-4'>
          <h2 className='text-xl font-semibold'>Different Sizes</h2>
          
          <div className='flex items-center space-x-2'>
            <Checkbox id='small' className='w-4 h-4' defaultChecked />
            <label htmlFor='small' className='cursor-pointer'>
              Small checkbox (w-4 h-4)
            </label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox id='medium' className='w-5 h-5' defaultChecked />
            <label htmlFor='medium' className='cursor-pointer'>
              Medium checkbox (w-5 h-5) - Default
            </label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox id='large' className='w-6 h-6' defaultChecked />
            <label htmlFor='large' className='cursor-pointer'>
              Large checkbox (w-6 h-6)
            </label>
          </div>
        </div>

        <div className='space-y-4'>
          <h2 className='text-xl font-semibold'>DaisyUI Variants</h2>
          
          <div className='flex items-center space-x-2'>
            <Checkbox id='primary' className='checkbox-primary' defaultChecked />
            <label htmlFor='primary' className='cursor-pointer'>
              Primary checkbox
            </label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox id='secondary' className='checkbox-secondary' defaultChecked />
            <label htmlFor='secondary' className='cursor-pointer'>
              Secondary checkbox
            </label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox id='accent' className='checkbox-accent' defaultChecked />
            <label htmlFor='accent' className='cursor-pointer'>
              Accent checkbox
            </label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox id='success' className='checkbox-success' defaultChecked />
            <label htmlFor='success' className='cursor-pointer'>
              Success checkbox
            </label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox id='warning' className='checkbox-warning' defaultChecked />
            <label htmlFor='warning' className='cursor-pointer'>
              Warning checkbox
            </label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox id='error' className='checkbox-error' defaultChecked />
            <label htmlFor='error' className='cursor-pointer'>
              Error checkbox
            </label>
          </div>
        </div>

        <div className='space-y-4'>
          <h2 className='text-xl font-semibold'>State Information</h2>
          <div className='bg-gray-100 p-4 rounded'>
            <p>Controlled checkbox 1 state: {checked1 ? 'Checked' : 'Unchecked'}</p>
            <p>Controlled checkbox 2 state: {checked2 ? 'Checked' : 'Unchecked'}</p>
          </div>
        </div>
      </div>
    </main>
  );
}
