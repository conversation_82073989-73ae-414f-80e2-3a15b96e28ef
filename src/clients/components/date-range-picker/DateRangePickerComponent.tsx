'use client';

import React from 'react';

import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

import { DateRange } from '@/clients/components/date-range-picker';

import CustomHeader from './CustomHeader';
import './style.css';

type Props = {
  value?: DateRange;
  onChange: (date: DateRange, event: React.SyntheticEvent) => void;
  filterDate?: (date: Date) => boolean;
  isMobile?: boolean;
  minDate?: Date;
  maxDate?: Date;
};

const DateRangePickerComponent = ({
  value = [null, null],
  onChange,
  isMobile,
  filterDate,
  minDate = new Date(),
  maxDate,
}: Props) => {
  const handleChange = (dates: [Date | null, Date | null], event: React.SyntheticEvent) => {
    onChange(dates, event);
  };

  const renderCustomHeader = (props: any): React.ReactNode => {
    return <CustomHeader {...props} />;
  };

  // Type assertion to handle React 19 compatibility issues with react-datepicker v4
  const DatePickerComponent = DatePicker as any;

  return (
    <div className='absolute top-[55px] right-[50%] translate-x-[50%] md:right-0 md:translate-x-0 z-[99999]'>
      <DatePickerComponent
        onChange={handleChange}
        startDate={value[0] || undefined}
        endDate={value[1] || undefined}
        selectsRange
        filterDate={filterDate}
        monthsShown={isMobile ? 1 : 2}
        calendarClassName='!flex !font-poppins'
        formatWeekDay={(nameOfDay: string) => nameOfDay.slice(0, 1)}
        renderCustomHeader={renderCustomHeader}
        dayClassName={() =>
          '!w-[40px] !h-[40px] !inline-flex !items-center !justify-center !rounded-[50%] !m-[1px]'
        }
        minDate={minDate}
        maxDate={maxDate}
        inline
      />
    </div>
  );
};

export default DateRangePickerComponent;
