'use client';

import { useCallback, useState } from 'react';

import Checkbox from '@/clients/ui/checkbox';

const CUISINE_OPTIONS = [
  'American',
  'Asian',
  'Bakery',
  'BBQ',
  'Burgers',
  'Candy',
  'Chinese',
  'Coffee',
  'French',
  'Ice Cream',
  'Indian',
  'Irish',
  'Italian',
  'International',
  'Japanese',
  'Mediterranean',
  'Mexican',
  'New England Coastal',
  'Pizza',
  'Sandwiches',
  'Seafood',
  'Steak',
  'Sushi',
  'Thai',
  'Venetian',
  'Vietnamese',
];

const CuisineFilterCheckboxes = () => {
  const onChangeCheckbox = useCallback((e: any) => {
    console.log('e', { e });
  }, []);

  return (
    <div className='px-5 pt-2.5 pb-5'>
      {CUISINE_OPTIONS.map((_c, index) => (
        <div key={index} className='flex items-center w-full space-x-2 mt-2'>
          <Checkbox id={_c} className='w-4 h-4' onChange={onChangeCheckbox} />
          <label htmlFor={_c} className='cursor-pointer text-sm text-[#71717A]'>
            {_c}
          </label>
        </div>
      ))}
    </div>
  );
};

export default CuisineFilterCheckboxes;
