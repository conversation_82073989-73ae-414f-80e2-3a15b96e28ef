'use client';

import { useCallback, useState } from 'react';

import LoadingSpinner from '@/app/ui/loading-spinner';
import { Nullable } from '@/types/common';

import dynamic from 'next/dynamic';

const BuyerInterestFormDialog = dynamic(() => import('./BuyerInterestFormDialog'), {
  ssr: false,
  loading: () => (
    <div className='fixed bg-black/40 inset-0 flex items-center justify-center text-white'>
      <LoadingSpinner className='w-10 h-10' />
    </div>
  ),
});

type Props = {
  listingId: Nullable<number>;
};

const BuyerInterestFormButton = ({ listingId }: Props) => {
  const [showForm, setShowForm] = useState<boolean>(false);

  const onToggle = useCallback(() => {
    setShowForm((_f) => !_f);
  }, []);

  return (
    <>
      <p className='text-[#334155] underline text-sm m-0 cursor-pointer' onClick={onToggle}>
        Get notified if this home comes on the market
      </p>
      {showForm && <BuyerInterestFormDialog onToggle={onToggle} listingId={listingId} />}
    </>
  );
};

export default BuyerInterestFormButton;
