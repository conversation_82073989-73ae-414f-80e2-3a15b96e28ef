'use client';

import React, { useCallback, useState } from 'react';

import { CheckCircleIcon, XMarkIcon } from '@heroicons/react/24/outline';

import { submitBookingRequest } from '@/app/services/rental-listing-details';
import InputLabel from '@/app/ui/input-label';
import { Separator } from '@/app/ui/separator';
import { DateRange } from '@/clients/components/date-range-picker';
import { useBooking } from '@/clients/contexts/BookingContext';
import useForm from '@/clients/hooks/useForm';
import Button from '@/clients/ui/button';
import { Input } from '@/clients/ui/input';
import Modal from '@/clients/ui/modal';
import Textarea from '@/clients/ui/textarea';
import { SegmentEvents } from '@/types/analytics';
import { ProgressStatus } from '@/types/common';
import { IListingDetails } from '@/types/rental-listing-details';

import dayjs from 'dayjs';

import CheckinCheckout from '../CheckinCheckout';
import GuestSelectorComponent from '../GuestSelectorComponent';

import { getFormValidators } from './helpers';

type Props = {
  open: boolean;
  onClose?: () => void;
  details: IListingDetails;
  isMobile?: boolean;
  dateRange?: DateRange;
  adultsCount?: number;
  childrenCount?: number;
};

export type FormValues = {
  dateRange: DateRange;
  adults: number;
  children: number;
  firstName: string;
  lastName: string;
  email: string;
  message: string;
  phone: string;
};

const RequestBookingForm = ({
  open,
  onClose,
  details,
  isMobile,
  dateRange = [null, null],
  adultsCount = 1,
  childrenCount = 0,
}: Props) => {
  const { date, guests, petCount, petType, isPetSelected, petDescription, resetValues } =
    useBooking();
  const {
    capacity,
    listing_id,
    address,
    area: { name: neighborhood },
    availabilities,
    rates,
  } = details;
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(null);
  const { formState, pristine, errors, onChange, preSubmitCheck } = useForm<FormValues>(
    {
      dateRange,
      adults: adultsCount,
      children: childrenCount,
      firstName: '',
      lastName: '',
      email: '',
      message: '',
      phone: '',
    },
    getFormValidators(capacity),
  );

  const onSubmit = useCallback(
    (e: React.ChangeEvent<HTMLFormElement>) => {
      e.preventDefault();
      const _errors = preSubmitCheck();
      if (Object.values(_errors).some((_error) => _error !== '')) {
        return;
      }
      setProgressStatus(ProgressStatus.LOADING);
      const hasValidDates = date && dayjs(date.from).isValid() && dayjs(date.to).isValid();

      submitBookingRequest({
        email: formState.email ?? '',
        phone: formState.phone ?? '',
        first_name: formState.firstName ?? '',
        last_name: formState.lastName ?? '',
        comment: formState.message ?? '',
        neighborhood,
        listing_id,
        guest: formState.adults ?? 1,
        children: formState?.children ?? 0,
        property_address: address,
        interest: 'rentals',
        arrival_date: hasValidDates ? dayjs(date.from).format('YYYY-MM-DD') : undefined,
        departure_date: hasValidDates ? dayjs(date.to).format('YYYY-MM-DD') : undefined,
        pet_count: isPetSelected ? petCount : undefined,
        pet_type: isPetSelected ? petType : undefined,
        pet_description: isPetSelected ? petDescription : undefined,
        contact_method: 'email',
        source: 'cnc',
      })
        .then(() => {
          setProgressStatus(ProgressStatus.SUCCESSFUL);
          window?.analytics?.track(SegmentEvents.BOOKING_REQUESTED, {
            amenities: details.featured_amenities,
            capacity: details.capacity,
            checkin_date: hasValidDates ? dayjs(formState.dateRange[0]).format('YYYY-MM-DD') : null,
            checkout_date: hasValidDates
              ? dayjs(formState.dateRange[1]).format('YYYY-MM-DD')
              : null,
            listing_id: details.listing_id,
            listing_name: details.address,
            listing_number_of_bedrooms: details.bedroom_number,
            neighborhood: details.area_name,
            num_adults: formState?.adults ?? 1,
            num_children: formState?.children ?? 0,
            number_of_days: dayjs(formState.dateRange[1]).diff(formState.dateRange[0], 'days'),
            number_of_guests: Number(formState?.adults ?? 1) + Number(formState?.children ?? 0),
            price: details.peak_rate,
            region: 'Massachusetts',
            city: 'Nantucket',
            country: 'United States',
            guest: guests.adults ?? 1,
            children: guests?.children ?? 0,
            arrival_date: hasValidDates ? dayjs(date.from).format('YYYY-MM-DD') : undefined,
            departure_date: hasValidDates ? dayjs(date.to).format('YYYY-MM-DD') : undefined,
            pet_count: isPetSelected ? petCount : undefined,
            pet_type: isPetSelected ? petType : undefined,
            pet_description: isPetSelected ? petDescription : undefined,
            url: document.URL,
            referrer: document.referrer,
          });
          resetValues();
        })
        .catch((e) => {
          console.log(e);
          setProgressStatus(ProgressStatus.FAILED);
        });
    },
    [
      preSubmitCheck,
      date,
      formState.email,
      formState.phone,
      formState.firstName,
      formState.lastName,
      formState.message,
      formState.adults,
      formState?.children,
      formState.dateRange,
      neighborhood,
      listing_id,
      address,
      isPetSelected,
      petCount,
      petType,
      petDescription,
      details.featured_amenities,
      details.capacity,
      details.listing_id,
      details.address,
      details.bedroom_number,
      details.area_name,
      details.peak_rate,
      guests.adults,
      guests?.children,
      resetValues,
    ],
  );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;
      onChange(value, name);
    },
    [onChange],
  );

  return (
    <Modal open={open} onClose={onClose} className='md:rounded-md min-h-[100vh] md:min-h-full'>
      <div className='px-4 md:px-5 py-5'>
        <div className='flex items-center justify-between mb-5'>
          <p className='text-2xl font-medium leading-[34px]'>Request Booking</p>
          <XMarkIcon onClick={onClose} className='w-5 h-5 cursor-pointer text-gray-main' />
        </div>
        <Separator className='mb-2' />
        {progressStatus === ProgressStatus.SUCCESSFUL ? (
          <div className='text-green-500 flex items-center'>
            <CheckCircleIcon className='w-5 h-5' />
            <span className='ml-2'>Request Successfully Sent</span>
          </div>
        ) : (
          <form onSubmit={onSubmit}>
            <div className='max-h-[80dvh] overflow-y-scroll'>
              <div className='mb-5'>
                <p className='font-semibold leading-[175%] text-dark-blue mb-4'>Trip Details</p>
                <CheckinCheckout
                  propertyDetails={details}
                  propertyId={details.listing_id}
                  isMobile={isMobile}
                />

                <GuestSelectorComponent
                  capacity={details.capacity}
                  petsAllowed={details?.requirement?.pet_allow?.toLowerCase() === 'true'}
                />
              </div>
              <div className='mb-5'>
                <p className='font-semibold leading-[175%] text-dark-blue mb-4'>
                  Traveler Information{' '}
                </p>
                <div className='grid grid-cols-2 gap-2 w-full mb-7'>
                  <Input
                    name='firstName'
                    type='text'
                    label='First Name'
                    className='p-2 w-full text-sm'
                    helperText={errors?.firstName ?? ''}
                    error={!!errors?.firstName?.length}
                    onChange={onChangeTextInput}
                    required
                  />
                  <Input
                    name='lastName'
                    type='text'
                    label='Last Name'
                    className='p-2 w-full text-sm'
                    helperText={errors?.lastName ?? ''}
                    error={!!errors?.lastName?.length}
                    onChange={onChangeTextInput}
                    required
                  />
                </div>
                <div className='grid grid-cols-2 gap-2 w-full mb-7'>
                  <Input
                    name='email'
                    type='email'
                    label='Email'
                    className='p-2 w-full text-sm'
                    helperText={errors?.email ?? ''}
                    error={!!errors?.email?.length}
                    onChange={onChangeTextInput}
                    required
                  />
                  <Input
                    name='phone'
                    type='text'
                    label='Phone Number'
                    className='p-2 w-full text-sm'
                    helperText={errors?.phone ?? ''}
                    error={!!errors?.phone?.length}
                    onChange={onChangeTextInput}
                    required
                  />
                </div>
              </div>
              <div className='mb-7'>
                <p className='font-semibold leading-[175%] text-dark-blue mb-2.5'>
                  Send a message to the Agent<span className='text-red-main'>*</span>
                </p>
                <InputLabel className='text-gray-80'>
                  Tell them about who are travelling with and why you chose this property.
                </InputLabel>
                <Textarea
                  name='message'
                  className='w-full p-2'
                  type='textarea'
                  helperText={errors?.message ?? ''}
                  error={!!errors?.message?.length}
                  onChange={onChangeTextInput}
                />
              </div>
              {progressStatus === ProgressStatus.FAILED && (
                <InputLabel error className='my-5'>
                  Failed to send request
                </InputLabel>
              )}
            </div>
            <Button
              disabled={progressStatus === ProgressStatus.LOADING}
              title={
                progressStatus === ProgressStatus.LOADING
                  ? 'Submitting...'
                  : 'Submit Booking Request'
              }
              className='h-[50px] w-full'
              isSubmit
            />
          </form>
        )}
      </div>
    </Modal>
  );
};

export default RequestBookingForm;
