'use client';

import React, { useCallback, useState } from 'react';

import { CalendarIcon, ChatBubbleOvalLeftIcon } from '@heroicons/react/24/outline';

import LoadingSpinner from '@/app/ui/loading-spinner';
import { useBooking } from '@/clients/contexts/BookingContext';
import Button from '@/clients/ui/button';
import { Skeleton } from '@/clients/ui/skeleton';
import { IListingDetails } from '@/types/rental-listing-details';
import { currencyFormatter, currencyFormatterRound } from '@/utils/common';

import classNames from 'classnames';
import { format } from 'date-fns';
import dynamic from 'next/dynamic';

import MobileBottomDrawer from '../common/MobileBottomDrawer';

import { MOBILE_CHECKOUT_CALENDAR_WRAPPER } from './CheckinCheckout';
import InquireButton from './Inquire/InquireButton';

const CheckoutDateRangePickerMobile = dynamic(
  () => import('./CheckoutDateRangePicker/CheckoutDateRangePickerMobile'),
  {
    loading: () => (
      <div className='fixed bg-black/40 inset-0 flex items-center justify-center text-white'>
        <LoadingSpinner className='w-10 h-10' />
      </div>
    ),
  },
);

const MobilePricesAndSummary = dynamic(() => import('./MobilePricesAndSummary'), {
  loading: () => (
    <div className='fixed bg-black/40 inset-0 flex items-center justify-center text-white'>
      <LoadingSpinner className='w-10 h-10' />
    </div>
  ),
});

type Props = {
  details: IListingDetails;
  petsAllowed?: boolean;
};

const StickyBookButton = ({ details, petsAllowed }: Props) => {
  const [openDatepicker, setOpenDatepicker] = useState<boolean>(false);
  const [openSummary, setOpenSummary] = useState<boolean>(false);
  const { date, setDate, rentInfo, isFetchingBookingDetails, setOpenBookingForm } = useBooking();

  const onToggleDatePicker = useCallback(() => {
    setOpenDatepicker(!openDatepicker);
  }, [openDatepicker]);

  const onToggleSummaryDialog = useCallback(() => {
    setOpenSummary(!openSummary);
  }, [openSummary]);

  const onSubmit = useCallback(() => {
    if (rentInfo) {
      setOpenBookingForm(true);
    } else {
      onToggleDatePicker();
    }
  }, [onToggleDatePicker, rentInfo, setOpenBookingForm]);

  return (
    <>
      <div className='fixed bottom-0 left-0 right-0 z-[5] bg-white px-4 py-5 shadow-card md:hidden rounded-t-2xl'>
        <div className={classNames({ 'pb-2': rentInfo })}>
          <div className='mb-5 flex items-center justify-between'>
            {!rentInfo ? (
              <>
                <p className='m-0 text-2xl font-medium'>
                  {currencyFormatterRound.format(details?.peak_rate)}/week
                </p>
              </>
            ) : (
              <>
                {isFetchingBookingDetails ? (
                  <>
                    <Skeleton className='w-[120px] h-4' />
                    <Skeleton className='w-[120px] h-4' />{' '}
                  </>
                ) : (
                  <>
                    <p
                      onClick={onToggleSummaryDialog}
                      role='presentation'
                      className='m-0 text-base font-medium underline w-[50%]'
                    >
                      {currencyFormatter.format(rentInfo?.rent ?? 0)}
                    </p>
                    <p
                      onClick={onToggleDatePicker}
                      role='presentation'
                      className='m-0 text-base font-medium underline w-[50%] truncate text-right'
                    >
                      {`${date?.from && format(date?.from, 'MMM d')} to ${
                        date?.to && format(date?.to, 'MMM d, yyyy')
                      }`}
                    </p>
                  </>
                )}
              </>
            )}
          </div>

          <div className='flex items-center justify-between gap-x-3'>
            <Button
              onClick={onSubmit}
              className='rounded-full font-medium w-[calc(50%-6px)] py-4 flex items-center gap-x-2 h-[50px]'
            >
              <CalendarIcon className='w-5 h-5' />
              {rentInfo ? `Reserve` : `Check Availability`}
            </Button>
            <InquireButton
              listingDetails={details}
              title='Inquire'
              icon={<ChatBubbleOvalLeftIcon className='w-4 h-4' />}
              className='rounded-full !border !border-solid !border-olive-variant text-black font-medium w-[calc(50%-6px)] flex items-center gap-x-2 h-[50px]'
            />
          </div>
        </div>
      </div>
      <MobileBottomDrawer open={openDatepicker} onToggle={onToggleDatePicker}>
        {openDatepicker && (
          <div id={MOBILE_CHECKOUT_CALENDAR_WRAPPER}>
            <CheckoutDateRangePickerMobile
              date={date}
              setDate={setDate}
              onClose={onToggleDatePicker}
              availableCalendar={details.availabilities.sort(
                (_a1, _a2) => new Date(_a1.from_date).getTime() - new Date(_a2.from_date).getTime(),
              )}
              rentInfo={rentInfo}
              rentalRates={details.rates}
              isFetchingBookingDetails={isFetchingBookingDetails}
            />
          </div>
        )}
      </MobileBottomDrawer>
      <MobileBottomDrawer open={openSummary} onToggle={onToggleSummaryDialog}>
        {openSummary && (
          <MobilePricesAndSummary
            onToggle={onToggleSummaryDialog}
            onToggleDatePicker={onToggleDatePicker}
            petsAllowed={petsAllowed}
            property={details}
          />
        )}
      </MobileBottomDrawer>
    </>
  );
};

export default StickyBookButton;
