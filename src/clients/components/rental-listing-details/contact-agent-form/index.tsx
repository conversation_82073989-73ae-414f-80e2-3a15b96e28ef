'use client';

import React, { useCallback, useEffect, useState } from 'react';

import { CheckCircleIcon, XMarkIcon } from '@heroicons/react/24/outline';

import { submitBookingRequest } from '@/app/services/rental-listing-details';
import InputLabel from '@/app/ui/input-label';
import { Separator } from '@/app/ui/separator';
import { useBooking } from '@/clients/contexts/BookingContext';
import useForm from '@/clients/hooks/useForm';
import Button from '@/clients/ui/button';
import { Input } from '@/clients/ui/input';
import Modal from '@/clients/ui/modal';
import Textarea from '@/clients/ui/textarea';
import { EMAIL_PATTERN, PHONE_NUMBER_PATTERN } from '@/constants/patterns';
import { SegmentEvents } from '@/types/analytics';
import { ProgressStatus } from '@/types/common';
import { IListingDetails } from '@/types/rental-listing-details';

import classNames from 'classnames';
import dayjs from 'dayjs';

import CheckinCheckout from '../CheckinCheckout';
import GuestSelectorComponent from '../GuestSelectorComponent';

type Props = {
  open: boolean;
  onClose?: () => void;
  details: IListingDetails;
};

export type FormValues = {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  message: string;
};

export enum ContactMethod {
  PHONE = 'phone',
  TEXT = 'text',
  EMAIL = 'email',
}

const ContactAgentForm = ({ open, onClose, details }: Props) => {
  const {
    listing_id,
    address,
    area: { name: neighborhood },
    featured_amenities,
  } = details;
  const { guests, date, petCount, isPetSelected, petType, petDescription, resetValues } =
    useBooking();
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(null);
  const [contactMethod, setContactMethod] = useState<ContactMethod>(ContactMethod.PHONE);
  const { formState, errors, onChange, preSubmitCheck } = useForm<FormValues>(
    {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      message: '',
    },
    {
      firstName: (_v, _n, _value) => {
        if (_value.trim().length === 0) {
          return `First name is required.`;
        }
      },
      lastName: (_v, _n, _value) => {
        if (_value.trim().length === 0) {
          return `Last name is required.`;
        }
      },
      email: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Email is required.`;
        }

        if (!_value.match(EMAIL_PATTERN)) {
          return 'Invalid email address';
        }
      },
      phone: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Phone number is required.`;
        }

        if (!_value.match(PHONE_NUMBER_PATTERN)) {
          return 'Invalid phone number';
        }
      },
      message: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Message is required.`;
        }
      },
    },
  );

  const onSubmit = useCallback(
    (e: React.ChangeEvent<HTMLFormElement>) => {
      e.preventDefault();
      const _errors = preSubmitCheck();
      if (Object.values(_errors).some((_error) => _error !== '')) {
        return;
      }
      setProgressStatus(ProgressStatus.LOADING);
      const hasValidDates = date && dayjs(date.from).isValid() && dayjs(date.to).isValid();
      submitBookingRequest({
        email: formState.email,
        first_name: formState.firstName,
        last_name: formState.lastName,
        comment: formState.message,
        phone: formState.phone,
        neighborhood,
        listing_id,
        property_address: address,
        contact_method: contactMethod,
        guest: guests.adults ?? 1,
        children: guests?.children ?? 0,
        arrival_date: hasValidDates ? dayjs(date.from).format('YYYY-MM-DD') : undefined,
        departure_date: hasValidDates ? dayjs(date.to).format('YYYY-MM-DD') : undefined,
        pet_count: isPetSelected ? petCount : undefined,
        pet_type: isPetSelected ? petType : undefined,
        pet_description: isPetSelected ? petDescription : undefined,
        source: 'cnc',
      })
        .then((data) => {
          setProgressStatus(ProgressStatus.SUCCESSFUL);
          window?.analytics?.track(SegmentEvents.CONTACT_AGENT, {
            email: formState.email,
            first_name: formState.firstName,
            last_name: formState.lastName,
            how_can_we_help: formState.message,
            phone: formState.phone,
            neighborhood,
            listing_id,
            property_address: address,
            contact_method: contactMethod,
            guest: guests.adults ?? 1,
            children: guests?.children ?? 0,
            arrival_date: hasValidDates ? dayjs(date.from).format('YYYY-MM-DD') : undefined,
            departure_date: hasValidDates ? dayjs(date.to).format('YYYY-MM-DD') : undefined,
            pet_count: isPetSelected ? petCount : undefined,
            pet_type: isPetSelected ? petType : undefined,
            pet_description: isPetSelected ? petDescription : undefined,
            url: document.URL,
            referrer: document.referrer,
          });
          resetValues();
        })
        .catch((e) => {
          console.log(e);
          setProgressStatus(ProgressStatus.FAILED);
        });
    },
    [
      preSubmitCheck,
      date,
      formState.email,
      formState.firstName,
      formState.lastName,
      formState.message,
      formState.phone,
      neighborhood,
      listing_id,
      address,
      contactMethod,
      guests.adults,
      guests?.children,
      isPetSelected,
      petCount,
      petType,
      petDescription,
      resetValues,
    ],
  );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;
      onChange(value, name);
    },
    [onChange],
  );

  useEffect(() => {}, []);

  return (
    <Modal open={open} onClose={onClose} className='md:rounded-md min-h-[100vh] md:min-h-full'>
      <div className='px-4 md:px-5 py-5'>
        <div className='flex items-center justify-between mb-5'>
          <p className='text-2xl font-medium leading-[34px]'>Ask A Question</p>
          <XMarkIcon onClick={onClose} className='w-5 h-5 cursor-pointer text-gray-main' />
        </div>
        <Separator className='mb-2' />

        {progressStatus === ProgressStatus.SUCCESSFUL ? (
          <div className='text-green-500 flex items-center'>
            <CheckCircleIcon className='w-5 h-5' />
            <span className='ml-2'>Request Successfully Sent</span>
          </div>
        ) : (
          <form onSubmit={onSubmit}>
            <div className='max-h-[80dvh] overflow-y-scroll'>
              <div className='pb-6'>
                <div className='mb-5'>
                  <p className='font-semibold leading-[175%] mb-5 text-dark-blue'>Trip Details</p>
                  <CheckinCheckout propertyDetails={details} propertyId={details.listing_id} />

                  <GuestSelectorComponent
                    capacity={details.capacity}
                    petsAllowed={details?.requirement?.pet_allow?.toLowerCase() === 'true'}
                  />
                </div>
                <div className='grid grid-cols-2 gap-2 w-full mb-6'>
                  <Input
                    name='firstName'
                    type='text'
                    label='First name'
                    className='p-2 w-full text-sm'
                    helperText={errors?.firstName ?? ''}
                    error={!!errors?.firstName?.length}
                    onChange={onChangeTextInput}
                  />
                  <Input
                    name='lastName'
                    type='text'
                    label='Last name'
                    className='p-2 w-full text-sm'
                    helperText={errors?.lastName ?? ''}
                    error={!!errors?.lastName?.length}
                    onChange={onChangeTextInput}
                  />
                </div>
                <div className='grid grid-cols-2 gap-2 w-full mb-6'>
                  <Input
                    name='email'
                    type='email'
                    label='Email'
                    className='p-2 w-full text-sm'
                    helperText={errors?.email ?? ''}
                    error={!!errors?.email?.length}
                    onChange={onChangeTextInput}
                  />
                  <Input
                    name='phone'
                    type='text'
                    label='Phone Number'
                    className='p-2 w-full text-sm'
                    helperText={errors?.phone ?? ''}
                    error={!!errors?.phone?.length}
                    onChange={onChangeTextInput}
                  />
                </div>
                <div className='mb-6'>
                  <InputLabel error={!!errors?.message?.length}>How we can help?</InputLabel>
                  <Textarea
                    name='message'
                    className='w-full p-2'
                    type='textarea'
                    placeholder='How we can help?'
                    helperText={errors?.message ?? ''}
                    error={!!errors?.message?.length}
                    onChange={onChangeTextInput}
                  />
                </div>
                <div className='mb-6'>
                  <InputLabel>How would you like to be contacted?</InputLabel>
                  <div className='flex items-center'>
                    <div
                      className={classNames(
                        'text-sm leading-[30px] px-3 bg-light-slate mr-2.5 rounded cursor-pointer',
                        contactMethod === ContactMethod.PHONE &&
                          'text-primary-slate border-primary-slate border',
                      )}
                      onClick={() => setContactMethod(ContactMethod.PHONE)}
                    >
                      Phone
                    </div>
                    <div
                      className={classNames(
                        'text-sm leading-[30px] px-3 bg-light-slate mr-2.5 rounded cursor-pointer',
                        contactMethod === ContactMethod.TEXT &&
                          'text-primary-slate border-primary-slate border',
                      )}
                      onClick={() => setContactMethod(ContactMethod.TEXT)}
                    >
                      Text
                    </div>
                    <div
                      className={classNames(
                        'text-sm leading-[30px] px-3 bg-light-slate mr-2.5 rounded cursor-pointer',
                        contactMethod === ContactMethod.EMAIL &&
                          'text-primary-slate border-primary-slate border',
                      )}
                      onClick={() => setContactMethod(ContactMethod.EMAIL)}
                    >
                      Email
                    </div>
                  </div>
                </div>
                {progressStatus === ProgressStatus.FAILED && (
                  <InputLabel error>Failed to send request</InputLabel>
                )}
              </div>
            </div>
            <Button
              disabled={progressStatus === ProgressStatus.LOADING}
              title={progressStatus === ProgressStatus.LOADING ? 'Submitting...' : 'Send Message'}
              className='h-[50px] w-full'
              isSubmit
            />
          </form>
        )}
      </div>
    </Modal>
  );
};

export default ContactAgentForm;
