'use client';

import { useCallback, useEffect, useMemo, useState } from 'react';

import { PhoneIcon } from '@heroicons/react/24/outline';

import FormHelperText from '@/app/ui/form-helper-text';
import InputLabel from '@/app/ui/input-label';
import { BookingFlowStep, useBooking } from '@/clients/contexts/BookingContext';
import Button from '@/clients/ui/button';
import { SegmentEvents } from '@/types/analytics';
import { Nullable, ProgressStatus } from '@/types/common';
import { IListingDetails } from '@/types/rental-listing-details';
import { getRentalStartMonth } from '@/utils/calendar';
import { currencyFormatterRound } from '@/utils/common';

import { differenceInCalendarDays } from 'date-fns';
import dayjs from 'dayjs';
import dynamic from 'next/dynamic';

import { DateRange } from '../date-range-picker';

import BuyerInterestFormButton from './BuyerInterestForm/BuyerInterestFormButton';
import CheckinCheckout from './CheckinCheckout';
import GuestAndPetSelector from './GuestAndPetSelector';
import InquireButton from './Inquire/InquireButton';

const RequestBookingForm = dynamic(() => import('./request-booking-from'), {
  ssr: false,
});
const ContactAgentForm = dynamic(() => import('./contact-agent-form'), {
  ssr: false,
});

const PricesAndSummary = dynamic(() => import('./PricesAndSummary'), {
  ssr: false,
});

type Props = {
  details: IListingDetails;
  isMobile?: boolean;
  petsAllowed?: boolean;
};

const PropertyCheckout = ({ details, isMobile, petsAllowed }: Props) => {
  const [isGuestsValid, setGuestValuesValid] = useState<boolean>(true);
  const {
    guests,
    setGuests,
    petCount,
    setPetCount,
    isPetSelected,
    petType,
    setPetType,
    petDescription,
    setPetDescription,
    rentInfo,
    setIsPetSelected,
    date,
    progressStatus,
    openContactAgentForm,
    setOpenContactAgentForm,
    openBookingForm,
    setOpenBookingForm,
    setStep,
  } = useBooking();
  const [datePickerError, setDatePickerError] = useState<Nullable<string>>(null);
  const [open, setOpen] = useState<boolean>(false);

  const numberOfNights = useMemo(
    () => (date?.from && date?.to ? differenceInCalendarDays(date.to, date.from) : 0),
    [date?.from, date?.to],
  );
  const [dateRange, setDateRange] = useState<DateRange>([getRentalStartMonth(), null]);

  const onToggle = useCallback(() => {
    setOpen((prev) => !prev);
  }, []);

  const onClickRequestBooking = useCallback(() => {
    if (!date?.from || !date?.to) {
      setDatePickerError('Please select dates');
      return;
    }
    if (progressStatus === ProgressStatus.LOADING || !isGuestsValid || !rentInfo) {
      return;
    }

    window?.analytics?.track(SegmentEvents.BOOKING_REQUEST_CLICKED, {
      action: 'CTA clicked',
      amenities: details.featured_amenities,
      capacity: details.capacity,
      checkin_date: dayjs(dateRange[0]).format('YYYY-MM-DD'),
      checkout_date: dayjs(dateRange[1]).format('YYYY-MM-DD'),
      listing_id: details.listing_id,
      listing_name: details.address,
      listing_number_of_bedrooms: details.bedroom_number,
      neighborhood: details.area_name,
      num_adults: guests.adults ?? 1,
      num_children: guests.children ?? 0,
      pet_count: isPetSelected ? petCount : undefined,
      pet_type: isPetSelected ? petType : undefined,
      pet_description: isPetSelected ? petDescription : undefined,
      number_of_days: dayjs(dateRange[1]).diff(dateRange[0], 'days'),
      number_of_guests: Number(guests.adults ?? 1) + Number(guests.children ?? 0),
      price: details.peak_rate,
      region: 'Massachusetts',
      city: 'Nantucket',
      country: 'United States',
      url: document.URL,
      referrer: document.referrer,
    });
    setStep(BookingFlowStep.DETAILS);
  }, [
    date?.from,
    date?.to,
    dateRange,
    details.address,
    details.area_name,
    details.bedroom_number,
    details.capacity,
    details.featured_amenities,
    details.listing_id,
    details.peak_rate,
    guests.adults,
    guests.children,
    isPetSelected,
    petCount,
    petDescription,
    petType,
    progressStatus,
    setStep,
    rentInfo,
    isGuestsValid,
  ]);

  useEffect(() => {
    if (details.capacity && guests.adults + guests.children > details.capacity) {
      setGuestValuesValid(false);
    } else {
      setGuestValuesValid(true);
    }
  }, [details.capacity, guests]);

  return (
    <>
      <div className='rounded-2xl rounded-b-none md:rounded-b-2xl border border-solid border-platinium shadow-card-25 p-6 bg-white'>
        <p className='m-0 text-[50px] font-medium'>
          {currencyFormatterRound.format(
            rentInfo?.rent ? (rentInfo?.rent / numberOfNights) * 7 : details?.peak_rate,
          )}
          <span className='text-base font-normal ml-2 !mb-3'>Per Week</span>
        </p>
        <div className='my-8'>
          <CheckinCheckout propertyDetails={details} propertyId={details.listing_id} />
          {datePickerError && (
            <FormHelperText className='pl-2.5' error>
              {datePickerError}
            </FormHelperText>
          )}

          <div className='mt-6'>
            <InputLabel className='px-[14px]'>GUESTS</InputLabel>
            <GuestAndPetSelector
              guestsValues={guests}
              setGuestsValues={setGuests}
              petCount={petCount}
              setPetCount={setPetCount}
              isPetSelected={isPetSelected}
              petsAllowed={petsAllowed}
              capacity={details.capacity}
              petType={petType}
              setPetType={setPetType}
              petDescription={petDescription}
              setPetDescription={setPetDescription}
              setIsPetSelected={setIsPetSelected}
            />
            <p className='m-0 mt-2 text-[10px] uppercase text-metal-gray px-3.5'>
              This property has a maximum of {details.capacity} guests. <br />
              {!petsAllowed
                ? `Pets are not allowed.`
                : `Pets allowed with prior permission, fees may apply.`}
            </p>
          </div>

          {rentInfo && <PricesAndSummary />}

          <div className='flex items-center gap-x-3 my-4'>
            <Button
              intent='primary'
              className='w-[50%] rounded-full py-4 font-medium border-none h-[54px]'
              disabled={progressStatus === ProgressStatus.LOADING}
              onClick={onClickRequestBooking}
            >
              {rentInfo ? `Reserve` : `Check Availability`}
            </Button>
            <InquireButton
              className='w-[50%] !border !border-solid rounded-full !border-olive-variant text-black h-[54px]'
              listingDetails={details}
              title='Inquire'
            />
          </div>
          <div className='px-6 py-4 rounded-2xl bg-light-slate mt-4 border border-solid border-[#BAE6FD]'>
            <p className='text-center text-lg font-medium m-0'>
              Prefer to talk? Call a rental specialist:
            </p>
            <a
              href='tel:************'
              className='bg-white border border-olive-variant border-solid flex items-center gap-x-2 px-4 py-2 my-2 rounded-full w-max mx-auto no-underline text-[#1E293B]'
            >
              <PhoneIcon className='w-5 h-5' />
              <span className='text-lg font-medium'>************</span>
            </a>
            <p className='text-[#64748B] text-sm text-center m-0'>
              Hours: 8am &#45; 8pm ET, 7 days a week
            </p>
          </div>
          <div className='px-6 py-4 rounded-2xl mt-4 border border-solid border-[#E2E8F0]'>
            <p className='text-xs font-medium m-0 italic uppercase text-[#64748B] leading-6'>
              Love this Home?
            </p>
            <BuyerInterestFormButton
              listingId={details.listing_id ? Number(details.listing_id) : null}
            />
          </div>
        </div>
        {openBookingForm && (
          <RequestBookingForm
            details={details}
            open={openBookingForm}
            isMobile={isMobile}
            onClose={() => setOpenBookingForm(false)}
            dateRange={dateRange}
            adultsCount={guests?.adults}
            childrenCount={guests?.children}
          />
        )}
        {openContactAgentForm && (
          <ContactAgentForm
            open={openContactAgentForm}
            onClose={() => setOpenContactAgentForm(false)}
            details={details}
          />
        )}
      </div>
    </>
  );
};

export default PropertyCheckout;
