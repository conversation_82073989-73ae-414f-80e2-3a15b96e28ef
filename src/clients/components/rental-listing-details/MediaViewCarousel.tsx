import { useEffect, useRef } from 'react';

import Slider from 'react-slick';

import Modal from '@/clients/ui/modal';
import { IListingImage } from '@/types/rental-listing-details';

import Image from 'next/image';

type Props = {
  images: IListingImage[];
  open: boolean;
  onClose: () => void;
};

const settings = {
  dots: false,
  infinite: true,
  speed: 500,
  slidesToShow: 1,
  slidesToScroll: 1,
};

const MediaViewCarousel = ({ images, open, onClose }: Props) => {
  const slickRef = useRef<Slider | null>(null);
  // Type assertion to work around react-slick compatibility issues with React 19
  const SliderComponent = Slider as any;

  useEffect(() => {
    const track = slickRef.current?.innerSlider?.list?.querySelector('.slick-track');
    const slide = track?.querySelector('.slick-slide') as HTMLDivElement;
    slide?.focus();
  }, []);

  return (
    <Modal
      open={open}
      onClose={onClose}
      className='min-w-[80%] bg-transparent px-12 shadow-none'
      showClose
    >
      <SliderComponent {...settings} className='px-[10%]' ref={slickRef}>
        {images.map((_image, index) => (
          <div key={index} id={`slide${index + 1}`} className='relative h-[80vh]'>
            <Image
              alt={_image?.alt_text ?? ''}
              src={_image.url}
              fill
              className='object-contain'
              sizes='(max-width: 600px) 80vw, 33vw'
              placeholder='blur'
              blurDataURL='https://placehold.co/150'
            />
            <span className='absolute bottom-4 right-4 rounded-[45px] bg-[rgba(15,15,15,.5)] px-5 py-1.5 text-sm text-white'>{`${
              index + 1
            } / ${images.length}`}</span>
          </div>
        ))}
      </SliderComponent>
    </Modal>
  );
};

export default MediaViewCarousel;
