'use client';

import { memo, useCallback, useMemo, useState } from 'react';

import { InformationCircleIcon } from '@heroicons/react/24/outline';

import { useBooking } from '@/clients/contexts/BookingContext';
import Button from '@/clients/ui/button';
import { Separator } from '@/clients/ui/separator';
import { currencyFormatterRound } from '@/utils/common';
import {
  calculateStateAndLocalTaxes,
  calculateTotalBeforeTaxes,
  formatBookingFeesData,
} from '@/utils/rentals';

import { differenceInCalendarDays } from 'date-fns';
import dynamic from 'next/dynamic';

const PriceBreakdownDialog = dynamic(() => import('./PriceBreakdownDialog'), { ssr: false });

type Props = {
  showDueToday?: boolean;
};

const PriceDetails = ({ showDueToday }: Props) => {
  const { rentInfo, isPetSelected, date, isInsuranceAdded } = useBooking();
  const [showPricesBreakdown, setShowPricesBreakdown] = useState<boolean>(false);
  const numberOfNights = useMemo(
    () => (date?.from && date?.to ? differenceInCalendarDays(date?.to, date?.from) : 0),
    [date?.from, date?.to],
  );
  const formattedPrices = useMemo(
    () => formatBookingFeesData(rentInfo, isPetSelected, numberOfNights),
    [isPetSelected, numberOfNights, rentInfo],
  );

  const onToggle = useCallback(() => {
    setShowPricesBreakdown((_s) => !_s);
  }, []);

  return (
    <>
      <div className='flex items-center justify-between mb-3'>
        <p className='m-0 font-medium'>Price Details</p>
        <Button intent='ghost' className='!p-0 text-xs font-medium' onClick={onToggle}>
          <InformationCircleIcon className='w-4 h-4 mr-2' />
          Price breakdown
        </Button>
      </div>
      <div className='flex items-center justify-between mt-2'>
        <p className='m-0 text-xs'>{numberOfNights} nights</p>
        <p className='m-0 text-xs'>
          {rentInfo &&
            currencyFormatterRound.format(calculateTotalBeforeTaxes(rentInfo, isPetSelected))}
        </p>
      </div>
      <div className='flex items-center justify-between mt-2'>
        <p className='m-0 text-xs'>Taxes</p>
        <p className='m-0 text-xs'>
          {rentInfo && currencyFormatterRound.format(calculateStateAndLocalTaxes(rentInfo))}
        </p>
      </div>
      <Separator className='my-4' />
      <div className='flex items-center justify-between mt-2'>
        <p className='m-0 text-xs font-semibold'>TOTAL</p>
        <p className='m-0 text-xs font-semibold'>
          {currencyFormatterRound.format(
            Number(formattedPrices?.grandTotal ?? 0) +
              Number(isInsuranceAdded ? (rentInfo?.travel_insurance_amount ?? 0) : 0),
          )}
        </p>
      </div>
      <p className='text-[10px] text-grey-main m-0'>
        Price to be confirmed by your rental specialist.
      </p>

      {showDueToday && (
        <>
          <div className='flex items-center justify-between mt-3'>
            <p className='m-0 text-xs font-semibold'>Deposit due today</p>
            <p className='m-0 text-xs font-semibold'>
              {currencyFormatterRound.format(Number(rentInfo?.payment_schedule?.[0][1] ?? 0))}
            </p>
          </div>
          <p className='text-[10px] text-grey-main m-0'>
            Payment will be processed only if the booking goes through.
          </p>
        </>
      )}
      {showPricesBreakdown && (
        <PriceBreakdownDialog open={showPricesBreakdown} onToggle={onToggle} />
      )}
    </>
  );
};

export default memo(PriceDetails);
